# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.8.1 (tags/v3.8.1:1b293b6, Dec 18 2019, 23:11:46) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: VSMsimuASIBN.py
# Compiled at: 2021-11-04 19:33:50
# Size of source mod 2**32: 7266 bytes
import ctypes, binascii, hashlib, datetime
from cryptography.hazmat.primitives.asymmetric.x25519 import X25519Private<PERSON><PERSON>, X25519PublicKey
from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from zenzefi import ZenZefi, ZenZefiException
import xml.etree.ElementTree as ET
from xml.sax.saxutils import escape
import logging, sys, os
from string import Template
import traceback
DEFAULT_SECRET = bytes([85, 119, 101, 77, 97, 114, 116, 105, 110, 74, 117, 108, 105, 97, 110, 
 65, 110, 100, 114, 101, 97, 115, 77, 97, 114, 107, 117, 115, 50, 
 48, 49, 54])
EPHEMERAL_PUBLIC_KEY = bytes([133, 32, 240, 9, 137, 48, 167, 84, 116, 139, 125, 220, 180, 62, 247, 
 90, 13, 191, 58, 13, 38, 56, 26, 244, 235, 164, 169, 142, 170, 155, 
 78, 106])
EPHEMERAL_PRIVATE_KEY = bytes([119, 7, 109, 10, 115, 24, 165, 125, 60, 22, 193, 114, 81, 178, 102, 
 69, 223, 76, 47, 135, 235, 192, 153, 42, 177, 119, 251, 165, 29, 
 185, 44, 42])
GOLDEN_ETM_VSM_PRIV_KEY = bytes([19, 236, 25, 45, 142, 27, 25, 241, 149, 129, 160, 32, 105, 14, 154, 
 24, 41, 212, 134, 86, 181, 154, 50, 206, 2, 172, 22, 192, 241, 165, 
 242, 43])
zenzefi = ZenZefi("https://localhost:61000")
script_path = os.path.abspath(os.path.dirname(sys.argv[0]))
cert_path = os.path.join(script_path, "certs")

def derive_shared_key(privatekey, publickey):
    privkey = X25519PrivateKey.from_private_bytes(privatekey)
    pubkey = X25519PublicKey.from_public_bytes(publickey)
    return privkey.exchange(pubkey)


def derive_sessionkey_and_iv(nonce, sharedsecret):
    h = hashlib.sha512()
    h.update(nonce + sharedsecret)
    digest = h.digest()
    return (digest[None[:16]], digest[(-16)[:None]])


def encrypt_carspecificsecret(nonce, sessionkey, iv):
    data = nonce + DEFAULT_SECRET + b'\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10'
    cipher = Cipher(algorithms.AES(sessionkey), modes.CBC(iv))
    encryptor = cipher.encryptor()
    return encryptor.update(data) + encryptor.finalize()


def calc_final_signature(vsm_cert, date_time, tick, tick_off, enc_css, ephVsmPubKey):
    data = date_time
    data += tick
    data += tick_off
    data += enc_css
    data += ephVsmPubKey
    if ssa_version == "3":
        vsm_privkey = Ed25519PrivateKey.from_private_bytes(GOLDEN_ETM_VSM_PRIV_KEY)
        sig = vsm_privkey.sign(data)
    else:
        sig = zenzefi.sign_ecu(vsm_cert.authority_key_identifier, data, vsm_cert.id, vsm_cert.serial_no)
    return sig


def get_current_date_in_binary():
    date = datetime.datetime.now()
    bindate = b''
    bindate += date.year.to_bytes(2, byteorder="big")
    bindate += date.month.to_bytes(1, byteorder="big")
    bindate += date.day.to_bytes(1, byteorder="big")
    bindate += date.hour.to_bytes(1, byteorder="big")
    bindate += date.minute.to_bytes(1, byteorder="big")
    bindate += date.second.to_bytes(1, byteorder="big")
    return bindate


def parse_input_xml(path):
    input = {}
    tree = ET.parse(path)
    root = tree.getroot()
    for child in root:
        input[child.tag] = child.text
    else:
        return input


def write_output_xml(path, date_time=b'', tick=b'', tick_off=b'', enc_css=b'', eph_vsm_pub_key=b'', sig=b'', vsm_ecu_cert=b'', etm_intermediate_cert=b'', etm_backend_cert=b'', error='', dbg_info=''):
    with open(os.path.join(script_path, "output_tmpl.xml"), "r") as ftmpl:
        tmpl = Template(ftmpl.read())
        xml_out = tmpl.substitute(dnt=(binascii.hexlify(date_time).decode("utf-8")),
          tc=(binascii.hexlify(tick).decode("utf-8")),
          tcOff=(binascii.hexlify(tick_off).decode("utf-8")),
          enccss=(binascii.hexlify(enc_css).decode("utf-8")),
          ephVsmPubKey=(binascii.hexlify(eph_vsm_pub_key).decode("utf-8")),
          sig=(binascii.hexlify(sig).decode("utf-8")),
          vsmEcuCert=(binascii.hexlify(vsm_ecu_cert).decode("utf-8")),
          etmInterimCert=(binascii.hexlify(etm_intermediate_cert).decode("utf-8")),
          etmBackendCert=(binascii.hexlify(etm_backend_cert).decode("utf-8")),
          error=(escape(error)),
          debugInfo=(escape(dbg_info)))
        with open(path, "w") as outxml:
            outxml.write(xml_out)


if len(sys.argv) != 4:
    print("Usage: %s ssa_version_major inputxml outputxml" % sys.argv[0])
    sys.exit(0)
ssa_version = sys.argv[1]
ssa_version = ssa_version.split(".")[0]
if ssa_version not in frozenset({'2', '1', '3'}):
    print("Only SSA version 1, 2 or 3 is supported.")
    sys.exit(0)
input_path = sys.argv[2]
output_path = sys.argv[3]
try:
    input = parse_input_xml(input_path)
except FileNotFoundError:
    print("Input XML file not found (%s)" % sys.argv[1])
    sys.exit(0)
except ET.ParseError as Ex:
    try:
        error = "XML malformed: %s" % str(Ex)
        print(error)
        write_output_xml(output_path, error=error)
        sys.exit(0)
    finally:
        Ex = None
        del Ex

except Exception:
    error = "Unknown error occured while parsing input xml file."
    print(error)
    write_output_xml(output_path, error=error, dbg_info=(traceback.format_exc()))
    sys.exit(0)
else:
    ecu_ephemeral_key = binascii.unhexlify(input["Ephemeral_ECU_Public_Key"])
    nonce = binascii.unhexlify(input["Nonce"])
    tick = bytes(5)
    tick_off = tick
    date_time = enc_css = sig = vsm_cert = vsm_cert_data = etm_intermediate_cert = etm_backend_cert = b''
    error = dbg_info = ""
    try:
        date_time = get_current_date_in_binary()
        sharedkey = derive_shared_key(EPHEMERAL_PRIVATE_KEY, ecu_ephemeral_key)
        sesssionkey, IV = derive_sessionkey_and_iv(nonce, sharedkey)
        enc_css = encrypt_carspecificsecret(nonce, sesssionkey, IV)
        if ssa_version == "3":
            with open(os.path.join(script_path, "certs", "ETMVSMSimu.crt"), "rb") as f_etm_vsm_simu_crt:
                vsm_cert_data = f_etm_vsm_simu_crt.read()
        else:
            vsm_cert = zenzefi.get_vsm_simu_cert(input["Backend_CA_SubjectKeyIdentifier"])
            vsm_cert_data = zenzefi.load_certificate_data(vsm_cert).data
        sig = calc_final_signature(vsm_cert, date_time, tick, tick_off, enc_css, EPHEMERAL_PUBLIC_KEY)
        if ssa_version == "3":
            with open(os.path.join(cert_path, "InterimIntermediate.der"), "rb") as f_intermediate_cert:
                etm_intermediate_cert = f_intermediate_cert.read()
            with open(os.path.join(cert_path, "InterimBackend.der"), "rb") as f_backend_cert:
                etm_backend_cert = f_backend_cert.read()
    except BaseException as ex:
        try:
            error = str(ex)
            dbg_info = traceback.format_exc()
        finally:
            ex = None
            del ex

    else:
        write_output_xml(output_path,
          date_time=date_time,
          tick=tick,
          tick_off=tick_off,
          enc_css=enc_css,
          eph_vsm_pub_key=EPHEMERAL_PUBLIC_KEY,
          sig=sig,
          vsm_ecu_cert=vsm_cert_data,
          etm_intermediate_cert=etm_intermediate_cert,
          etm_backend_cert=etm_backend_cert,
          error=error,
          dbg_info=dbg_info)
