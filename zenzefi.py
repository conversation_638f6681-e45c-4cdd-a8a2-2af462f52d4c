# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.8.1 (tags/v3.8.1:1b293b6, Dec 18 2019, 23:11:46) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: C:\Code_Projects\VSMsimuASIBN\zenzefi.py
# Compiled at: 2021-09-23 16:40:33
# Size of source mod 2**32: 10981 bytes
import requests, base64
from typing import Optional, Dict, List, Set

class ZenZefiException(BaseException):
    __doc__ = "\n    Exception raise for zenzefi operation\n    "


class ZenZefiCertificate:
    __doc__ = "\n    Represents a ZenZefi certificate\n    "
    STATE_ISSUED = "ISSUED"
    SATE_SIGNING_REQUEST = "SIGNING_REQUEST"
    STATE_VIRTUAL = "VIRTUAL"
    TYPE_NO_TYPE = "NO_TYPE"
    TYPE_BACKEND_CA_CERTIFICATE = "BACKEND_CA_CERTIFICATE"
    TYPE_BACKEND_CA_LINK_CERTIFICATE = "BACKEND_CA_LINK_CERTIFICATE"
    TYPE_ROOT_CA_CERTIFICATE = "ROOT_CA_CERTIFICATE"
    TYPE_ROOT_CA_LINK_CERTIFICATE = "ROOT_CA_LINK_CERTIFICATE"
    TYPE_ECU_CERTIFICATE = "ECU_CERTIFICATE"
    TYPE_DIAGNOSTIC_AUTHENTICATION_CERTIFICATE = "DIAGNOSTIC_AUTHENTICATION_CERTIFICATE"
    TYPE_ENHANCED_RIGHTS_CERTIFICATE = "ENHANCED_RIGHTS_CERTIFICATE"
    TYPE_TIME_CERTIFICATE = "TIME_CERTIFICATE"
    TYPE_VARIANT_CODE_USER_CERTIFICATE = "VARIANT_CODE_USER_CERTIFICATE"
    TYPE_VARIANT_CODING_DEVICE_CERTIFICATE = "VARIANT_CODING_DEVICE_CERTIFICATE"
    TYPE_SEC_OC_IS = "SEC_OC_IS"
    TYPE_VIRTUAL_FOLDER = "VIRTUAL_FOLDER"
    ROLE_SUPPLIER = "Supplier"
    ROLE_DEVELOPMENT_ENH = "Development ENHANCED"
    ROLE_PRODUCTION = "Production"
    ROLE_AFTER_SALES_ENH = "After-Sales ENHANCED"
    ROLE_AFTER_SALES_STD = "After-Sales STANDARD"
    ROLE_AFTER_SALES_BASIC = "After-Sales BASIC"
    ROLE_TEST_TOOL = "Internal Diagnostic Test Tool"
    ROLE_EPTI_TOOL = "ePTI Test Tool"
    ATTRIBUTE_MAPPING = {
     'activeForTesting': '"active_for_testing"', 
     'algorithmIdentifier': '"algorithm_identifier"', 
     'authorityKeyIdentifier': '"authority_key_identifier"', 
     'baseCertificateId': '"base_certificate_id"', 
     'basicConstraints': '"basic_constraints"', 
     'basicConstraintsText': '"basic_constraints_text"', 
     'description': '"description"', 
     'forVsmSimulation': '"for_vsm_simulation"', 
     'hasChildren': '"has_children"', 
     'id': '"id"', 
     'issuer': '"issuer"', 
     'issuerSerialNumber': '"issuer_serial_number"', 
     'keyUsage': '"key_usage"', 
     'keyUsageText': '"key_usage_text"', 
     'nonce': '"nonce"', 
     'parentId': '"parent_id"', 
     'pkirole': '"pki_role"', 
     'prodQualifier': '"prod_qualifier"', 
     'secOCISCert': '"sec_ocis_cert"', 
     'serialNo': '"serial_no"', 
     'services': '"services"', 
     'signature': '"signature"', 
     'specialECU': '"special_ecu"', 
     'state': '"state"', 
     'status': '"status"', 
     'subject': '"subject"', 
     'subjectKeyIdentifier': '"subject_key_identifier"', 
     'subjectPublicKey': '"subject_public_key"', 
     'targetECU': '"target_ecu"', 
     'targetSubjectKeyIdentifier': '"target_subject_key_identifier"', 
     'type': '"type"', 
     'uniqueECUID': '"unique_ecu_id"', 
     'userRole': '"user_role"', 
     'validFrom': '"valid_from"', 
     'validTo': '"valid_to"', 
     'validToDateTime': '"valid_to_date_time"', 
     'validityStrengthColor': '"validity_strength_color"', 
     'version': '"version"', 
     'data': '"data"'}

    def __init__(self):
        self.active_for_testing = False
        self.algorithm_identifier = ""
        self.authority_key_identifier = ""
        self.base_certificate_id = ""
        self.basic_constraints = ""
        self.basic_constraints_text = ""
        self.description = ""
        self.for_vsm_simulation = False
        self.has_children = False
        self.id = ""
        self.issuer = ""
        self.issuer_serial_number = ""
        self.key_usage = []
        self.key_usage_text = ""
        self.nonce = ""
        self.parent_id = ""
        self.pki_role = ""
        self.prod_qualifier = ""
        self.sec_ocis_cert = False
        self.serial_no = ""
        self.services = ""
        self.signature = ""
        self.special_ecu = ""
        self.state = ZenZefiCertificate.STATE_ISSUED
        self.status = ""
        self.subject = ""
        self.subject_key_identifier = ""
        self.subject_public_key = ""
        self.target_ecu = ""
        self.target_subject_key_identifier = ""
        self.target_vin = ""
        self.type = ZenZefiCertificate.TYPE_NO_TYPE
        self.unique_ecu_id = ""
        self.user_role = ""
        self.valid_from = ""
        self.valid_to = ""
        self.valid_to_date_time = ""
        self.validity_strength_color = ""
        self.version = ""
        self.data = None

    def hydrate(self, data_dict):
        """
        Hydrates the given json data to this object
        :param data_dict Dictionary to use for filling this object
        """
        for key, value in data_dict.items():
            attr = self.ATTRIBUTE_MAPPING.get(key, None)
            if attr:
                setattr(self, attr, value)

    def __str__(self):
        return str(self.__dict__)

    @staticmethod
    def deserialize(data_dict):
        """
        Deserializes the given data dictionary into a ZenZefi certificate
        :param data_dict: Dictionary to use to deserialize the object
        :return Created Instance of ZenZefiCertificate
        """
        cert = ZenZefiCertificate()
        cert.hydrate(data_dict)
        return cert

    @staticmethod
    def base64_encode(data):
        """
        Base64 encodes a Byte-String (separated with dashes)
        :param data: Byte-String to encode (e.g. DE-AD-BE-EF)
        :return: Base64 representation
        """
        data = data.replace("-", "")
        data = bytearray.fromhex(data)
        return base64.b64encode(data).decode()


class ZenZefi:
    API_CERTIFICATES = "/certificates"
    API_CERTIFICATES_SEARCH = API_CERTIFICATES + "/search"
    API_CERTIFICATES_SIGN_ECU = API_CERTIFICATES + "/signECU"
    base_url = ""
    verify_ssl = False
    http_timeout = 15

    def __init__(self, url):
        self.base_url = url
        self.post_headers = {"Content-Type": "application/json"}

    def post_request(self, apiurl, data):
        return requests.post((self.base_url + apiurl),
          json=data,
          verify=(self.verify_ssl),
          timeout=(self.http_timeout),
          headers=(self.post_headers))

    def get_all_raw_certificates(self) -> List[Dict]:
        """
        Get list of certificate data dictionary
        """
        try:
            response = requests.get((self.base_url + ZenZefi.API_CERTIFICATES),
              verify=(self.verify_ssl),
              timeout=(self.http_timeout),
              headers=(self.post_headers))
            if response.status_code != 200:
                raise ZenZefiException("Failed to request all certificates")
            return response.json()
        except requests.RequestException as e:
            try:
                self.logger.error((self.tracking_uuid), error=f"Failed to retrieve the certificates: {e}")
                raise ZenZefiException(f"Failed to retrieve the certificates: {e}")
            finally:
                e = None
                del e

    def get_all_certificates(self) -> List[ZenZefiCertificate]:
        """
        Get a list of all certificates available for the currently logged in user
        :return: List of certificates
        """
        return [ZenZefiCertificate.deserialize(raw_certificate) for raw_certificate in self.get_all_raw_certificates()]

    def sign_ecu(self, bskid, data, ecuId, ecuSerialNo):
        data = {'backendSubjectKeyIdentifier':(ZenZefiCertificate.base64_encode)(bskid), 
         'challenge':(base64.b64encode(data).decode)(), 
         'ecuId':(ZenZefiCertificate.base64_encode)(ecuId), 
         'ecuSerialNumber':(ZenZefiCertificate.base64_encode)(ecuSerialNo)}
        resp = self.post_request(self.API_CERTIFICATES_SIGN_ECU, data)
        if resp.status_code == 200:
            json = resp.json()
            return base64.b64decode(json["signature"])
        if resp.status_code == 400:
            json = resp.json()
            raise ZenZefiException(json["errorMessage"])
        raise ZenZefiException("Zenzefi: unknown error - /certificates/signECU returned %s HTTP status code" % resp.status_code)

    def get_vsm_simu_cert(self, bskid):
        all_certs = self.get_all_certificates()
        i = 0
        for cert in all_certs:
            if cert.authority_key_identifier.replace("-", "") != bskid.replace("-", ""):
                pass
            elif cert.unique_ecu_id != "VSM-Simulation":
                pass
            elif cert.special_ecu == "1":
                return cert
        else:
            raise ZenZefiException("VSM simulation certificate with backend subject key identifier %s not found" % bskid)

    def load_certificate_data(self, cert: ZenZefiCertificate) -> ZenZefiCertificate:
        """
        Loads the certificate data for a specified certificate object
        :param cert: ZenZefiCertificate to load the certificate into
        :return: Filled ZenZefiCertificate
        """
        if not cert.authority_key_identifier:
            raise ZenZefiException("Could not load diag certificate data, cause of missing authority key identifier")
        aki = ZenZefiCertificate.base64_encode(cert.authority_key_identifier)
        serial = ZenZefiCertificate.base64_encode(cert.serial_no)
        try:
            params = {'AuthorityKeyIdentifier':aki, 
             'SerialNumber':serial}
            response = requests.get((self.base_url + ZenZefi.API_CERTIFICATES_SEARCH),
              params=params,
              verify=(self.verify_ssl),
              timeout=(self.http_timeout),
              headers=(self.post_headers))
            if response.status_code == 404:
                raise ZenZefiException("Could not find certificate data")
            if response.status_code != 200:
                raise ZenZefiException(f"Failed to load certificate data, HTTP status code: {response.status_code}")
            raw_data = response.json()
            cert.data = base64.b64decode(raw_data["certificateData"])
            return cert
        except Exception as e:
            try:
                self.logger.error((self.tracking_uuid), error=f"Failed to retrieve the certificate data: {e}")
                raise ZenZefiException(f"Failed to retrieve the certificate data: {e}")
            finally:
                e = None
                del e
